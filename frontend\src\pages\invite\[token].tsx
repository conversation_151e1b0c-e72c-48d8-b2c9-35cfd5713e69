/**
 * 邀请链接处理页面
 * 路由: /invite/:token
 */

import { TeamOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Helmet, history, useParams } from '@umijs/max';
import {
  Button,
  Card,
  Space,
  Typography,
  Result,
  Spin,
  Alert,
  Divider,
} from 'antd';
import { createStyles } from 'antd-style';
import React, { useState, useEffect } from 'react';
import { Footer } from '@/components';
import { InvitationService } from '@/services';
import type { InvitationInfoResponse } from '@/types/api';
import { TokenManager } from '@/utils/request';
import Settings from '../../../config/defaultSettings';

const { Title, Text, Paragraph } = Typography;

const useStyles = createStyles(({ token }) => {
  return {
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
      backgroundSize: '100% 100%',
    },
    content: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '32px 16px',
    },
    header: {
      marginBottom: 40,
      textAlign: 'center',
    },
    logo: {
      marginBottom: 16,
    },
    title: {
      marginBottom: 0,
    },
    inviteCard: {
      width: '100%',
      maxWidth: 500,
      boxShadow: token.boxShadowTertiary,
    },
    footer: {
      marginTop: 40,
      textAlign: 'center',
    },
  };
});

const InvitePage: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [invitationInfo, setInvitationInfo] = useState<InvitationInfoResponse | null>(null);
  const [result, setResult] = useState<any>(null);
  const { styles } = useStyles();

  // 获取邀请信息
  const fetchInvitationInfo = async () => {
    if (!token) {
      setResult({
        type: 'error',
        title: '邀请链接无效',
        message: '邀请链接格式错误或已过期',
      });
      setLoading(false);
      return;
    }

    try {
      const response = await InvitationService.getInvitationInfo(token);

      if (response.success) {
        setInvitationInfo(response);
      } else {
        setResult({
          type: 'error',
          title: '邀请链接无效',
          message: response.errorMessage || '无法获取邀请信息',
        });
      }
    } catch (error) {
      console.error('获取邀请信息失败:', error);
      setResult({
        type: 'error',
        title: '获取邀请信息失败',
        message: '网络错误，请稍后重试',
      });
    } finally {
      setLoading(false);
    }
  };

  // 处理邀请接受
  const handleAcceptInvitation = async () => {
    if (!token) return;

    setProcessing(true);
    try {
      const response = await InvitationService.acceptInvitationByLink(token, {});

      if (response.success) {
        // 如果是新用户且返回了访问令牌，自动登录
        if (response.isNewUser && response.accessToken) {
          try {
            TokenManager.setToken(response.accessToken);
            console.log('新用户自动创建并登录成功（无需密码）');

            setResult({
              type: 'success',
              title: '欢迎加入团队！',
              message: `您的账号已自动创建并登录（无需密码）。${response.nextAction || '正在跳转到个人中心...'}`,
              teamName: response.teamName,
              isNewUser: response.isNewUser,
              autoLogin: true,
            });

            // 延迟跳转到个人中心
            setTimeout(() => {
              history.push('/personal-center');
            }, 3000);
          } catch (error) {
            console.error('自动登录失败:', error);
            setResult({
              type: 'success',
              title: '账号创建成功！',
              message: '您的账号已成功创建（无需密码），请使用邮箱验证码登录后查看团队信息。',
              teamName: response.teamName,
              isNewUser: response.isNewUser,
              autoLogin: false,
            });
          }
        } else if (response.isNewUser) {
          // 新用户但没有自动登录令牌
          setResult({
            type: 'success',
            title: '账号创建成功！',
            message: '您的账号已成功创建（无需密码），请使用邮箱验证码登录后查看团队信息。',
            teamName: response.teamName,
            isNewUser: response.isNewUser,
            autoLogin: false,
          });
        } else {
          // 现有用户
          setResult({
            type: 'success',
            title: '加入成功！',
            message: response.nextAction || '您已成功加入团队，请登录后查看团队信息。',
            teamName: response.teamName,
            isNewUser: response.isNewUser,
            autoLogin: false,
          });
        }
      } else {
        setResult({
          type: 'error',
          title: '加入失败',
          message: response.errorMessage || '处理邀请时发生错误',
        });
      }
    } catch (error) {
      console.error('处理邀请失败:', error);
      setResult({
        type: 'error',
        title: '加入失败',
        message: '网络错误，请稍后重试',
      });
    } finally {
      setProcessing(false);
    }
  };

  // 处理取消操作
  const handleCancel = () => {
    history.push('/');
  };

  // 邀请确认界面
  const InvitationConfirm = () => {
    if (!invitationInfo) return null;

    return (
      <div style={{ textAlign: 'center', padding: '40px 20px' }}>
        <TeamOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 24 }} />
        <Title level={3}>团队邀请</Title>

        <div style={{ marginBottom: 32 }}>
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <div>
              <Text type="secondary">您被邀请加入团队：</Text>
              <br />
              <Title level={4} style={{ margin: '8px 0', color: '#1890ff' }}>
                {invitationInfo.teamName}
              </Title>
            </div>

            {invitationInfo.inviterName && (
              <div>
                <Text type="secondary">邀请人：</Text>
                <Text strong>{invitationInfo.inviterName}</Text>
              </div>
            )}

            {invitationInfo.message && (
              <div>
                <Text type="secondary">邀请消息：</Text>
                <br />
                <Text italic>"{invitationInfo.message}"</Text>
              </div>
            )}
          </Space>
        </div>

        <Divider />

        <div style={{ marginTop: 24 }}>
          <Title level={4} style={{ marginBottom: 24 }}>
            您确定要加入此团队吗？
          </Title>

          <Space size="large">
            <Button
              type="primary"
              size="large"
              loading={processing}
              onClick={handleAcceptInvitation}
              icon={<CheckCircleOutlined />}
              disabled={invitationInfo.isExpired}
            >
              {processing ? '正在处理...' : '确认加入'}
            </Button>
            <Button
              size="large"
              onClick={handleCancel}
              icon={<CloseCircleOutlined />}
              disabled={processing}
            >
              取消
            </Button>
          </Space>
        </div>

        {invitationInfo.isExpired && (
          <Alert
            message="邀请已过期"
            description="此邀请链接已过期，请联系团队管理员重新发送邀请。"
            type="warning"
            showIcon
            style={{ marginTop: 24 }}
          />
        )}
      </div>
    );
  };

  // 结果展示
  const ResultDisplay = () => {
    if (!result) return null;

    // 根据结果类型和自动登录状态显示不同的按钮
    const getExtraButtons = () => {
      if (result.type === 'success') {
        if (result.autoLogin) {
          // 自动登录成功，显示跳转到个人中心的按钮
          return [
            <Button type="primary" key="personal-center" onClick={() => history.push('/personal-center')}>
              前往个人中心
            </Button>,
          ];
        } else if (result.isNewUser) {
          // 新用户但未自动登录，引导去登录
          return [
            <Button type="primary" key="login" onClick={() => history.push('/user/login')}>
              前往登录
            </Button>,
            <Button key="home" onClick={() => history.push('/')}>
              返回首页
            </Button>,
          ];
        } else {
          // 现有用户，引导去登录
          return [
            <Button type="primary" key="login" onClick={() => history.push('/user/login')}>
              前往登录
            </Button>,
            <Button key="home" onClick={() => history.push('/')}>
              返回首页
            </Button>,
          ];
        }
      } else {
        // 错误情况，显示重试和返回首页
        return [
          <Button type="primary" key="retry" onClick={() => window.location.reload()}>
            重试
          </Button>,
          <Button key="home" onClick={() => history.push('/')}>
            返回首页
          </Button>,
        ];
      }
    };

    return (
      <Result
        status={result.type}
        title={result.title}
        subTitle={result.message}
        extra={getExtraButtons()}
      />
    );
  };

  // 页面加载时获取邀请信息
  useEffect(() => {
    fetchInvitationInfo();
  }, [token]);

  // 加载中状态
  if (loading) {
    return (
      <div className={styles.container}>
        <Helmet>
          <title>
            团队邀请
            {Settings.title && ` - ${Settings.title}`}
          </title>
        </Helmet>
        <div className={styles.content}>
          <Card className={styles.inviteCard}>
            <div style={{ textAlign: 'center', padding: '60px 20px' }}>
              <Spin size="large" />
              <div style={{ marginTop: 16 }}>
                <Text type="secondary">正在加载邀请信息...</Text>
              </div>
            </div>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  // 显示结果页面
  if (result) {
    return (
      <div className={styles.container}>
        <Helmet>
          <title>
            团队邀请
            {Settings.title && ` - ${Settings.title}`}
          </title>
        </Helmet>
        <div className={styles.content}>
          <Card className={styles.inviteCard}>
            <ResultDisplay />
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  // 显示邀请确认页面
  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          团队邀请
          {Settings.title && ` - ${Settings.title}`}
        </title>
      </Helmet>
      <div className={styles.content}>
        <div className={styles.header}>
          <Space direction="vertical" align="center" size="large">
            <div className={styles.logo}>
              <img src="/logo.svg" alt="TeamAuth" height={48} />
            </div>
            <div className={styles.title}>
              <Title level={2}>团队邀请</Title>
              <Text type="secondary">加入团队，开始协作</Text>
            </div>
          </Space>
        </div>

        <Card className={styles.inviteCard}>
          <InvitationConfirm />
        </Card>

        <div className={styles.footer}>
          <Text type="secondary">© 2025 TeamAuth. All rights reserved.</Text>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default InvitePage;
