{"version": 3, "sources": ["src/pages/team/components/TeamListContent.tsx", "src/pages/team/index.tsx"], "sourcesContent": ["/**\n * 团队列表内容组件\n */\n\nimport {\n  CheckCircleOutlined,\n  CrownOutlined,\n  EyeOutlined,\n  SearchOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\nimport {\n  Avatar,\n  Button,\n  Empty,\n  Input,\n  List,\n  message,\n  Space,\n  Tag,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { AuthService, TeamService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\nimport { getTeamIdFromCurrentToken, hasTeamInCurrentToken, getUserIdFromCurrentToken } from '@/utils/tokenUtils';\nimport { recordTeamSelection } from '@/utils/teamSelectionUtils';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\n\n\n\nconst TeamListContent: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [filteredTeams, setFilteredTeams] = useState<TeamDetailResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [currentTeamId, setCurrentTeamId] = useState<number | null>(null);\n\n  useEffect(() => {\n    fetchTeams();\n    // 获取当前团队ID\n    const teamId = getTeamIdFromCurrentToken();\n    setCurrentTeamId(teamId);\n  }, []);\n\n  useEffect(() => {\n    // 过滤团队列表\n    const filtered = teams.filter(\n      (team) =>\n        team.name.toLowerCase().includes(searchText.toLowerCase()) ||\n        (team.description &&\n          team.description.toLowerCase().includes(searchText.toLowerCase())),\n    );\n    setFilteredTeams(filtered);\n  }, [teams, searchText]);\n\n  const fetchTeams = async () => {\n    try {\n      setLoading(true);\n      const teamList = await TeamService.getUserTeams();\n      setTeams(teamList);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n      message.error('获取团队列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateTeam = () => {\n    history.push('/personal-center');\n  };\n\n  const handleViewTeam = async (team: TeamDetailResponse) => {\n    try {\n      // 切换到该团队\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === team.id\n      ) {\n        message.success(`已切换到团队：${team.name}`);\n        // 更新当前团队ID\n        setCurrentTeamId(team.id);\n        // 不需要刷新页面，让应用自然响应状态变化\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error: any) {\n      console.error('切换团队失败:', error);\n\n      // 响应拦截器已经处理了错误消息显示，这里只需要记录日志\n      // 如果是网络错误或其他非业务错误，才显示通用错误消息\n      if (!error.message || error.message === 'Failed to fetch') {\n        message.error('切换团队失败，请检查网络连接');\n      }\n    }\n  };\n\n  const handleSwitchTeam = async (team: TeamDetailResponse) => {\n    try {\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === team.id\n      ) {\n        message.success(`已切换到团队：${team.name}`);\n\n        // 记录用户选择了这个团队\n        const currentUserId = getUserIdFromCurrentToken();\n        if (currentUserId) {\n          recordTeamSelection(currentUserId, team.id);\n        }\n\n        // 更新当前团队ID\n        setCurrentTeamId(team.id);\n\n        // 等待一段时间确保 Token 更新完成后再跳转\n        setTimeout(() => {\n          history.push('/');\n        }, 200);\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error: any) {\n      console.error('切换团队失败:', error);\n\n      // 响应拦截器已经处理了错误消息显示，这里只需要记录日志\n      // 如果是网络错误或其他非业务错误，才显示通用错误消息\n      if (!error.message || error.message === 'Failed to fetch') {\n        message.error('切换团队失败，请检查网络连接');\n      }\n    }\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: 16 }}>\n        <Search\n          placeholder=\"搜索团队名称或描述\"\n          allowClear\n          value={searchText}\n          onChange={(e) => setSearchText(e.target.value)}\n          style={{ width: 300 }}\n        />\n      </div>\n\n      {filteredTeams.length === 0 && !loading ? (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description={\n            searchText ? '没有找到匹配的团队' : '您还没有加入任何团队'\n          }\n        >\n          {!searchText && (\n            <Button type=\"primary\" onClick={handleCreateTeam}>\n              创建第一个团队\n            </Button>\n          )}\n        </Empty>\n      ) : (\n        <List\n          loading={loading}\n          itemLayout=\"horizontal\"\n          dataSource={filteredTeams}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个团队`,\n          }}\n          renderItem={(team) => {\n            const isCurrentTeam = currentTeamId === team.id;\n            return (\n              <List.Item\n                style={{\n                  backgroundColor: isCurrentTeam ? '#f6ffed' : undefined,\n                  border: isCurrentTeam ? '1px solid #b7eb8f' : undefined,\n                  borderRadius: isCurrentTeam ? '8px' : undefined,\n                  padding: isCurrentTeam ? '16px' : undefined,\n                }}\n                actions={[\n                  <Button\n                    key=\"view\"\n                    type=\"text\"\n                    icon={<EyeOutlined />}\n                    onClick={() => handleViewTeam(team)}\n                  >\n                    查看详情\n                  </Button>,\n                  isCurrentTeam ? (\n                    <Tag\n                      key=\"current\"\n                      color=\"green\"\n                      icon={<CheckCircleOutlined />}\n                    >\n                      当前团队\n                    </Tag>\n                  ) : (\n                    <Button\n                      key=\"switch\"\n                      type=\"primary\"\n                      size=\"small\"\n                      onClick={() => handleSwitchTeam(team)}\n                    >\n                      进入团队\n                    </Button>\n                  ),\n                ]}\n              >\n                <List.Item.Meta\n                  avatar={\n                    <Avatar\n                      size={64}\n                      icon={<TeamOutlined />}\n                      style={{\n                        backgroundColor: isCurrentTeam ? '#52c41a' : '#1890ff',\n                        border: isCurrentTeam ? '2px solid #389e0d' : undefined,\n                      }}\n                    />\n                  }\n                  title={\n                    <Space>\n                      <Title level={4} style={{ margin: 0 }}>\n                        {team.name}\n                      </Title>\n                      {isCurrentTeam && (\n                        <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                          当前团队\n                        </Tag>\n                      )}\n                      {team.isCreator && (\n                        <Tag color=\"gold\" icon={<CrownOutlined />}>\n                          创建者\n                        </Tag>\n                      )}\n                    </Space>\n                  }\n                  description={\n                    <Space direction=\"vertical\" size=\"small\">\n                      {team.description && (\n                        <Text type=\"secondary\">{team.description}</Text>\n                      )}\n                      <Space>\n                        <Space size=\"small\">\n                          <UserOutlined />\n                          <Text type=\"secondary\">{team.memberCount} 名成员</Text>\n                        </Space>\n                        {team.assignedAt && (\n                          <Text type=\"secondary\">\n                            加入于 {new Date(team.assignedAt).toLocaleDateString()}\n                          </Text>\n                        )}\n                        <Text type=\"secondary\">\n                          创建于 {new Date(team.createdAt).toLocaleDateString()}\n                        </Text>\n                      </Space>\n                      <Space>\n                        <Text type=\"secondary\">状态：</Text>\n                        <Tag color={team.isActive ? 'green' : 'red'}>\n                          {team.isActive ? '启用' : '停用'}\n                        </Tag>\n                        {team.lastAccessTime && (\n                          <Text type=\"secondary\">\n                            最后访问：{new Date(team.lastAccessTime).toLocaleDateString()}\n                          </Text>\n                        )}\n                      </Space>\n                    </Space>\n                  }\n                />\n              </List.Item>\n            );\n          }}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default TeamListContent;\n", "/**\n * 我的团队页面 - 简化版，只保留团队列表和切换功能\n */\n\nimport { PageContainer } from '@ant-design/pro-components';\nimport React from 'react';\n\n// 导入团队列表组件\nimport TeamListContent from './components/TeamListContent';\n\nconst MyTeamsPage: React.FC = () => {\n  return (\n    <PageContainer title=\"我的团队\">\n      <TeamListContent />\n    </PageContainer>\n  );\n};\n\nexport default MyTeamsPage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BAiSD;;;eAAA;;;;;;8BAxRO;4BACiB;6BAWjB;wEACoC;iCACF;mCAEmD;2CACxD;;;;;;;;;;AAEpC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,WAAK;AAIxB,MAAM,kBAA4B;;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;IAElE,IAAA,gBAAS,EAAC;QACR;QACA,WAAW;QACX,MAAM,SAAS,IAAA,qCAAyB;QACxC,iBAAiB;IACnB,GAAG,EAAE;IAEL,IAAA,gBAAS,EAAC;QACR,SAAS;QACT,MAAM,WAAW,MAAM,MAAM,CAC3B,CAAC,OACC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtD,KAAK,WAAW,IACf,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEpE,iBAAiB;IACnB,GAAG;QAAC;QAAO;KAAW;IAEtB,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qBAAW,CAAC,YAAY;YAC/C,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,YAAO,CAAC,IAAI,CAAC;IACf;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,SAAS;YACT,MAAM,WAAW,MAAM,qBAAW,CAAC,SAAS,CAAC;gBAAE,QAAQ,KAAK,EAAE;YAAC;YAE/D,kBAAkB;YAClB,IACE,SAAS,oBAAoB,IAC7B,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE,EAC5B;gBACA,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC;gBACrC,WAAW;gBACX,iBAAiB,KAAK,EAAE;YACxB,sBAAsB;YACxB,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,aAAO,CAAC,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,WAAW;YAEzB,6BAA6B;YAC7B,4BAA4B;YAC5B,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,KAAK,mBACtC,aAAO,CAAC,KAAK,CAAC;QAElB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,qBAAW,CAAC,SAAS,CAAC;gBAAE,QAAQ,KAAK,EAAE;YAAC;YAE/D,kBAAkB;YAClB,IACE,SAAS,oBAAoB,IAC7B,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE,EAC5B;gBACA,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC;gBAErC,cAAc;gBACd,MAAM,gBAAgB,IAAA,qCAAyB;gBAC/C,IAAI,eACF,IAAA,uCAAmB,EAAC,eAAe,KAAK,EAAE;gBAG5C,WAAW;gBACX,iBAAiB,KAAK,EAAE;gBAExB,0BAA0B;gBAC1B,WAAW;oBACT,YAAO,CAAC,IAAI,CAAC;gBACf,GAAG;YACL,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,aAAO,CAAC,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,WAAW;YAEzB,6BAA6B;YAC7B,4BAA4B;YAC5B,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,KAAK,mBACtC,aAAO,CAAC,KAAK,CAAC;QAElB;IACF;IAEA,qBACE,2BAAC;;0BACC,2BAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;0BAC7B,cAAA,2BAAC;oBACC,aAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oBAC7C,OAAO;wBAAE,OAAO;oBAAI;;;;;;;;;;;YAIvB,cAAc,MAAM,KAAK,KAAK,CAAC,wBAC9B,2BAAC,WAAK;gBACJ,OAAO,WAAK,CAAC,sBAAsB;gBACnC,aACE,aAAa,cAAc;0BAG5B,CAAC,4BACA,2BAAC,YAAM;oBAAC,MAAK;oBAAU,SAAS;8BAAkB;;;;;;;;;;qCAMtD,2BAAC,UAAI;gBACH,SAAS;gBACT,YAAW;gBACX,YAAY;gBACZ,YAAY;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;gBACxC;gBACA,YAAY,CAAC;oBACX,MAAM,gBAAgB,kBAAkB,KAAK,EAAE;oBAC/C,qBACE,2BAAC,UAAI,CAAC,IAAI;wBACR,OAAO;4BACL,iBAAiB,gBAAgB,YAAY;4BAC7C,QAAQ,gBAAgB,sBAAsB;4BAC9C,cAAc,gBAAgB,QAAQ;4BACtC,SAAS,gBAAgB,SAAS;wBACpC;wBACA,SAAS;0CACP,2BAAC,YAAM;gCAEL,MAAK;gCACL,oBAAM,2BAAC,kBAAW;;;;;gCAClB,SAAS,IAAM,eAAe;0CAC/B;+BAJK;;;;;4BAON,8BACE,2BAAC,SAAG;gCAEF,OAAM;gCACN,oBAAM,2BAAC,0BAAmB;;;;;0CAC3B;+BAHK;;;;uDAON,2BAAC,YAAM;gCAEL,MAAK;gCACL,MAAK;gCACL,SAAS,IAAM,iBAAiB;0CACjC;+BAJK;;;;;yBAQT;kCAED,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;4BACb,sBACE,2BAAC,YAAM;gCACL,MAAM;gCACN,oBAAM,2BAAC,mBAAY;;;;;gCACnB,OAAO;oCACL,iBAAiB,gBAAgB,YAAY;oCAC7C,QAAQ,gBAAgB,sBAAsB;gCAChD;;;;;;4BAGJ,qBACE,2BAAC,WAAK;;kDACJ,2BAAC;wCAAM,OAAO;wCAAG,OAAO;4CAAE,QAAQ;wCAAE;kDACjC,KAAK,IAAI;;;;;;oCAEX,+BACC,2BAAC,SAAG;wCAAC,OAAM;wCAAQ,oBAAM,2BAAC,0BAAmB;;;;;kDAAK;;;;;;oCAInD,KAAK,SAAS,kBACb,2BAAC,SAAG;wCAAC,OAAM;wCAAO,oBAAM,2BAAC,oBAAa;;;;;kDAAK;;;;;;;;;;;;4BAMjD,2BACE,2BAAC,WAAK;gCAAC,WAAU;gCAAW,MAAK;;oCAC9B,KAAK,WAAW,kBACf,2BAAC;wCAAK,MAAK;kDAAa,KAAK,WAAW;;;;;;kDAE1C,2BAAC,WAAK;;0DACJ,2BAAC,WAAK;gDAAC,MAAK;;kEACV,2BAAC,mBAAY;;;;;kEACb,2BAAC;wDAAK,MAAK;;4DAAa,KAAK,WAAW;4DAAC;;;;;;;;;;;;;4CAE1C,KAAK,UAAU,kBACd,2BAAC;gDAAK,MAAK;;oDAAY;oDAChB,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;0DAGrD,2BAAC;gDAAK,MAAK;;oDAAY;oDAChB,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;kDAGpD,2BAAC,WAAK;;0DACJ,2BAAC;gDAAK,MAAK;0DAAY;;;;;;0DACvB,2BAAC,SAAG;gDAAC,OAAO,KAAK,QAAQ,GAAG,UAAU;0DACnC,KAAK,QAAQ,GAAG,OAAO;;;;;;4CAEzB,KAAK,cAAc,kBAClB,2BAAC;gDAAK,MAAK;;oDAAY;oDACf,IAAI,KAAK,KAAK,cAAc,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASxE;;;;;;;;;;;;AAKV;GA9PM;KAAA;IAgQN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnSf;;CAEC;;;;4BAgBD;;;eAAA;;;;;;;sCAd8B;uEACZ;iFAGU;;;;;;;;;AAE5B,MAAM,cAAwB;IAC5B,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,wBAAe;;;;;;;;;;AAGtB;KANM;IAQN,WAAe"}