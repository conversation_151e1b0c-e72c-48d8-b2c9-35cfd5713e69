{"version": 3, "sources": ["umi.5264699545402795781.hot-update.js", "src/app.tsx", "src/requestErrorConfig.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='7315735437539561497';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';\nimport { history } from '@umijs/max';\nimport {\n  ErrorBoundary,\n  Footer,\n} from '@/components';\nimport UserFloatButton from '@/components/FloatButton';\nimport { AuthService, UserService, TeamService } from '@/services';\nimport type { UserProfileResponse, TeamDetailResponse } from '@/types/api';\nimport { hasTeamInCurrentToken } from '@/utils/tokenUtils';\nimport { setupDevVerificationCodeListener, showDevDebugPanel } from '@/utils/devHelper';\nimport { errorConfig } from '@/requestErrorConfig';\n\n// 全局初始化数据配置，用于 Layout 用户信息和权限初始化\n// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate\nexport async function getInitialState(): Promise<{\n  currentUser?: UserProfileResponse;\n  currentTeam?: TeamDetailResponse;\n  loading?: boolean;\n  fetchUserInfo?: () => Promise<UserProfileResponse | undefined>;\n  fetchTeamInfo?: () => Promise<TeamDetailResponse | undefined>;\n}> {\n  const fetchUserInfo = async (): Promise<UserProfileResponse | undefined> => {\n    try {\n      return await UserService.getUserProfile();\n    } catch (error) {\n      console.error('获取用户信息失败:', error);\n      // 如果获取用户信息失败，可能是token过期，清除登录状态\n      AuthService.clearToken();\n      return undefined;\n    }\n  };\n\n  const fetchTeamInfo = async (): Promise<TeamDetailResponse | undefined> => {\n    try {\n      // 只有当Token中包含团队信息时才尝试获取团队详情\n      if (!hasTeamInCurrentToken()) {\n        return undefined;\n      }\n      return await TeamService.getCurrentTeamDetail();\n    } catch (error) {\n      console.error('获取团队信息失败:', error);\n      return undefined;\n    }\n  };\n\n\n\n  // 如果不是登录页面，执行\n  const { location } = history;\n  if (!['/user/login', '/user/register'].includes(location.pathname)) {\n    try {\n      // 检查登录状态\n      if (!AuthService.isLoggedIn()) {\n        return {\n          fetchUserInfo,\n          fetchTeamInfo,\n        };\n      }\n\n      // 获取用户信息\n      const currentUser = await fetchUserInfo();\n      if (!currentUser) {\n        return {\n          fetchUserInfo,\n          fetchTeamInfo,\n        };\n      }\n\n      // 尝试获取团队信息（如果Token中包含团队信息）\n      const currentTeam = await fetchTeamInfo();\n\n      return {\n        fetchUserInfo,\n        fetchTeamInfo,\n        currentUser,\n        currentTeam,\n      };\n    } catch (error) {\n      console.error('初始化失败:', error);\n      return {\n        fetchUserInfo,\n        fetchTeamInfo,\n      };\n    }\n  }\n\n  // 初始化开发环境调试工具\n  if (process.env.NODE_ENV === 'development') {\n    setupDevVerificationCodeListener();\n    // 延迟显示调试面板，避免在初始化时显示\n    setTimeout(() => {\n      if (window.location.pathname.includes('/user/login')) {\n        showDevDebugPanel();\n      }\n    }, 1000);\n  }\n\n  return {\n    fetchUserInfo,\n    fetchTeamInfo,\n  };\n}\n\n// ProLayout 支持的api https://procomponents.ant.design/components/layout\nexport const layout: RunTimeLayoutConfig = ({ initialState }) => {\n  return {\n    // 水印\n    waterMarkProps: {\n      content: initialState?.currentUser?.name,\n    },\n    // 底部版权信息\n    footerRender: () => <Footer />,\n    // 移除右侧内容渲染\n    rightContentRender: false,\n    // 页面切换时触发\n    onPageChange: () => {\n      const { location } = history;\n      // 如果没有登录，重定向到 login\n      if (\n        !initialState?.currentUser &&\n        !['/user/login', '/user/register'].includes(location.pathname)\n      ) {\n        history.push('/user/login');\n      }\n    },\n    // 自定义布局区域的背景颜色、文字颜色等\n    bgLayoutImgList: [\n      {\n        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',\n        left: 85,\n        bottom: 100,\n        height: '303px',\n      },\n      {\n        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',\n        bottom: -68,\n        right: -45,\n        height: '303px',\n      },\n      {\n        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',\n        bottom: 0,\n        left: 0,\n        width: '331px',\n      },\n    ],\n    // 自定义链接\n    links: [],\n    // 自定义菜单头\n    menuHeaderRender: undefined,\n    // 自定义 403 页面\n    // unAccessible: <div>unAccessible</div>,\n    // 增加一个 loading 的状态\n    childrenRender: (children) => {\n      return (\n        <ErrorBoundary>\n          {children}\n          {/* 全局 FloatButton - 在用户登录后显示 */}\n          <UserFloatButton />\n        </ErrorBoundary>\n      );\n    },\n    // 移除 settings 属性，使用默认配置\n  };\n};\n\n/**\n * @name request 配置，可以配置错误处理\n * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。\n * @doc https://umijs.org/docs/max/request#配置\n */\nexport const request: RequestConfig = {\n  ...errorConfig,\n};\n", "import type { RequestOptions } from '@@/plugin-request/request';\nimport type { RequestConfig } from '@umijs/max';\nimport { history } from '@umijs/max';\nimport { message, notification } from 'antd';\nimport { AuthService } from '@/services';\n\n// 错误处理方案： 错误类型\nenum ErrorShowType {\n  SILENT = 0,\n  WARN_MESSAGE = 1,\n  ERROR_MESSAGE = 2,\n  NOTIFICATION = 3,\n  REDIRECT = 9,\n}\n\n// 与后端约定的响应数据格式\ninterface ApiResponseStructure {\n  code: number;\n  message: string;\n  data: any;\n  timestamp: string;\n}\n\n// 兼容原有格式\ninterface ResponseStructure {\n  success: boolean;\n  data: any;\n  errorCode?: number;\n  errorMessage?: string;\n  showType?: ErrorShowType;\n}\n\n/**\n * @name 错误处理\n * 适配团队管理系统的错误处理机制\n * @doc https://umijs.org/docs/max/request#配置\n */\nexport const errorConfig: RequestConfig = {\n  // 错误处理： umi@3 的错误处理方案。\n  errorConfig: {\n    // 错误抛出\n    errorThrower: (res) => {\n      // 检查是否是我们的API响应格式\n      const apiResponse = res as unknown as ApiResponseStructure;\n      if (apiResponse.code !== undefined) {\n        // 使用我们的API响应格式\n        if (apiResponse.code !== 200) {\n          const error: any = new Error(apiResponse.message);\n          error.name = 'ApiError';\n          error.info = {\n            errorCode: apiResponse.code,\n            errorMessage: apiResponse.message,\n            data: apiResponse.data,\n            showType:\n              apiResponse.code === 401\n                ? ErrorShowType.REDIRECT\n                : ErrorShowType.ERROR_MESSAGE,\n          };\n          throw error;\n        }\n      } else {\n        // 兼容原有格式\n        const { success, data, errorCode, errorMessage, showType } =\n          res as unknown as ResponseStructure;\n        if (!success) {\n          const error: any = new Error(errorMessage);\n          error.name = 'BizError';\n          error.info = { errorCode, errorMessage, showType, data };\n          throw error;\n        }\n      }\n    },\n    // 错误接收及处理\n    errorHandler: (error: any, opts: any) => {\n      if (opts?.skipErrorHandler) throw error;\n\n      // 我们的 API 错误\n      if (error.name === 'ApiError') {\n        const errorInfo = error.info;\n        if (errorInfo) {\n          const { errorMessage, errorCode } = errorInfo;\n\n          // 处理认证错误\n          if (errorCode === 401) {\n            // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题\n            const currentPath = window.location.pathname;\n            const isDashboardRelated =\n              currentPath.startsWith('/dashboard') ||\n              currentPath.startsWith('/team');\n\n            if (isDashboardRelated) {\n              console.warn('Dashboard页面认证失败，可能是Token更新时序问题');\n              // 不立即清除Token和跳转，让页面自己处理\n              return;\n            }\n\n            // 其他页面立即处理认证错误\n            AuthService.clearTokens();\n            message.error('登录已过期，请重新登录');\n            history.push('/user/login');\n            return;\n          }\n\n          // 处理权限错误\n          if (errorCode === 403) {\n            // 显示后端返回的具体错误消息\n            message.error(errorMessage || '没有权限访问该资源');\n            return;\n          }\n\n          // 处理其他业务错误\n          switch (errorInfo.showType) {\n            case ErrorShowType.SILENT:\n              // do nothing\n              break;\n            case ErrorShowType.WARN_MESSAGE:\n              message.warning(errorMessage);\n              break;\n            case ErrorShowType.ERROR_MESSAGE:\n              message.error(errorMessage);\n              break;\n            case ErrorShowType.NOTIFICATION:\n              notification.open({\n                description: errorMessage,\n                message: `错误代码: ${errorCode}`,\n              });\n              break;\n            case ErrorShowType.REDIRECT:\n              history.push('/user/login');\n              break;\n            default:\n              message.error(errorMessage);\n          }\n        }\n      }\n      // 兼容原有的 BizError\n      else if (error.name === 'BizError') {\n        const errorInfo: ResponseStructure | undefined = error.info;\n        if (errorInfo) {\n          const { errorMessage, errorCode } = errorInfo;\n          switch (errorInfo.showType) {\n            case ErrorShowType.SILENT:\n              break;\n            case ErrorShowType.WARN_MESSAGE:\n              message.warning(errorMessage);\n              break;\n            case ErrorShowType.ERROR_MESSAGE:\n              message.error(errorMessage);\n              break;\n            case ErrorShowType.NOTIFICATION:\n              notification.open({\n                description: errorMessage,\n                message: errorCode,\n              });\n              break;\n            case ErrorShowType.REDIRECT:\n              history.push('/user/login');\n              break;\n            default:\n              message.error(errorMessage);\n          }\n        }\n      }\n      // HTTP 错误\n      else if (error.response) {\n        const { status } = error.response;\n        if (status === 401) {\n          AuthService.clearTokens();\n          message.error('登录已过期，请重新登录');\n          history.push('/user/login');\n        } else if (status === 403) {\n          message.error('没有权限访问该资源');\n        } else if (status === 404) {\n          message.error('请求的资源不存在');\n        } else if (status >= 500) {\n          message.error('服务器错误，请稍后重试');\n        } else {\n          message.error(`请求失败: ${status}`);\n        }\n      }\n      // 网络错误\n      else if (error.request) {\n        message.error('网络错误，请检查网络连接');\n      }\n      // 其他错误\n      else {\n        message.error('请求失败，请重试');\n      }\n    },\n  },\n\n  // 请求拦截器\n  requestInterceptors: [\n    (config: RequestOptions) => {\n      // 添加认证头\n      const token = AuthService.getToken();\n      if (token) {\n        config.headers = {\n          ...config.headers,\n          Authorization: `Bearer ${token}`,\n        };\n      }\n\n      // 确保 Content-Type\n      if (!config.headers?.['Content-Type']) {\n        config.headers = {\n          ...config.headers,\n          'Content-Type': 'application/json',\n        };\n      }\n\n      return config;\n    },\n  ],\n\n  // 响应拦截器\n  responseInterceptors: [\n    (response) => {\n      // 拦截响应数据，进行个性化处理\n      const { data } = response;\n\n      // 检查是否是我们的API响应格式\n      if (data?.code !== undefined) {\n        // 如果是成功响应，直接返回\n        if (data.code === 200) {\n          return response;\n        }\n        // 错误响应会在 errorThrower 中处理\n      }\n\n      return response;\n    },\n  ],\n};\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCYS,eAAe;2BAAf;;gBA0FT,MAAM;2BAAN;;gBAmEA,OAAO;2BAAP;;;;;;;wCA3KW;+CAIjB;yFACqB;6CAC0B;+CAEhB;8CAC8B;uDACxC;;;;;;;;;YAIrB,eAAe;gBAOpB,MAAM,gBAAgB;oBACpB,IAAI;wBACF,OAAO,MAAM,qBAAW,CAAC,cAAc;oBACzC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,+BAA+B;wBAC/B,qBAAW,CAAC,UAAU;wBACtB,OAAO;oBACT;gBACF;gBAEA,MAAM,gBAAgB;oBACpB,IAAI;wBACF,4BAA4B;wBAC5B,IAAI,CAAC,IAAA,iCAAqB,KACxB,OAAO;wBAET,OAAO,MAAM,qBAAW,CAAC,oBAAoB;oBAC/C,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,OAAO;oBACT;gBACF;gBAIA,cAAc;gBACd,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAO;gBAC5B,IAAI,CAAC;oBAAC;oBAAe;iBAAiB,CAAC,QAAQ,CAAC,SAAS,QAAQ,GAC/D,IAAI;oBACF,SAAS;oBACT,IAAI,CAAC,qBAAW,CAAC,UAAU,IACzB,OAAO;wBACL;wBACA;oBACF;oBAGF,SAAS;oBACT,MAAM,cAAc,MAAM;oBAC1B,IAAI,CAAC,aACH,OAAO;wBACL;wBACA;oBACF;oBAGF,2BAA2B;oBAC3B,MAAM,cAAc,MAAM;oBAE1B,OAAO;wBACL;wBACA;wBACA;wBACA;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,UAAU;oBACxB,OAAO;wBACL;wBACA;oBACF;gBACF;gBAKA,IAAA,2CAAgC;gBAChC,qBAAqB;gBACrB,WAAW;oBACT,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBACpC,IAAA,4BAAiB;gBAErB,GAAG;gBAGL,OAAO;oBACL;oBACA;gBACF;YACF;YAGO,MAAM,SAA8B,CAAC,EAAE,YAAY,EAAE;oBAI7C;gBAHb,OAAO;oBACL,KAAK;oBACL,gBAAgB;wBACd,OAAO,EAAE,yBAAA,oCAAA,4BAAA,aAAc,WAAW,cAAzB,gDAAA,0BAA2B,IAAI;oBAC1C;oBACA,SAAS;oBACT,cAAc,kBAAM,2BAAC,kBAAM;;;;;oBAC3B,WAAW;oBACX,oBAAoB;oBACpB,UAAU;oBACV,cAAc;wBACZ,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAO;wBAC5B,oBAAoB;wBACpB,IACE,EAAC,yBAAA,mCAAA,aAAc,WAAW,KAC1B,CAAC;4BAAC;4BAAe;yBAAiB,CAAC,QAAQ,CAAC,SAAS,QAAQ,GAE7D,YAAO,CAAC,IAAI,CAAC;oBAEjB;oBACA,qBAAqB;oBACrB,iBAAiB;wBACf;4BACE,KAAK;4BACL,MAAM;4BACN,QAAQ;4BACR,QAAQ;wBACV;wBACA;4BACE,KAAK;4BACL,QAAQ;4BACR,OAAO;4BACP,QAAQ;wBACV;wBACA;4BACE,KAAK;4BACL,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;qBACD;oBACD,QAAQ;oBACR,OAAO,EAAE;oBACT,SAAS;oBACT,kBAAkB;oBAClB,aAAa;oBACb,yCAAyC;oBACzC,mBAAmB;oBACnB,gBAAgB,CAAC;wBACf,qBACE,2BAAC,yBAAa;;gCACX;8CAED,2BAAC,oBAAe;;;;;;;;;;;oBAGtB;gBAEF;YACF;YAOO,MAAM,UAAyB;gBACpC,GAAG,+BAAW;YAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCCzIa;;;2BAAA;;;;;wCAnCW;yCACc;6CACV;;;;;;;;;;YAE5B,eAAe;sBACV;;;;;;eAAA,kBAAA;YA8BE,MAAM,cAA6B;gBACxC,uBAAuB;gBACvB,aAAa;oBACX,OAAO;oBACP,cAAc,CAAC;wBACb,kBAAkB;wBAClB,MAAM,cAAc;wBACpB,IAAI,YAAY,IAAI,KAAK,WACvB,eAAe;wBACf;4BAAA,IAAI,YAAY,IAAI,KAAK,KAAK;gCAC5B,MAAM,QAAa,IAAI,MAAM,YAAY,OAAO;gCAChD,MAAM,IAAI,GAAG;gCACb,MAAM,IAAI,GAAG;oCACX,WAAW,YAAY,IAAI;oCAC3B,cAAc,YAAY,OAAO;oCACjC,MAAM,YAAY,IAAI;oCACtB,UACE,YAAY,IAAI,KAAK;gCAGzB;gCACA,MAAM;4BACR;wBAAA,OACK;4BACL,SAAS;4BACT,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,GACxD;4BACF,IAAI,CAAC,SAAS;gCACZ,MAAM,QAAa,IAAI,MAAM;gCAC7B,MAAM,IAAI,GAAG;gCACb,MAAM,IAAI,GAAG;oCAAE;oCAAW;oCAAc;oCAAU;gCAAK;gCACvD,MAAM;4BACR;wBACF;oBACF;oBACA,UAAU;oBACV,cAAc,CAAC,OAAY;wBACzB,IAAI,iBAAA,2BAAA,KAAM,gBAAgB,EAAE,MAAM;wBAElC,aAAa;wBACb,IAAI,MAAM,IAAI,KAAK,YAAY;4BAC7B,MAAM,YAAY,MAAM,IAAI;4BAC5B,IAAI,WAAW;gCACb,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG;gCAEpC,SAAS;gCACT,IAAI,cAAc,KAAK;oCACrB,0CAA0C;oCAC1C,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oCAC5C,MAAM,qBACJ,YAAY,UAAU,CAAC,iBACvB,YAAY,UAAU,CAAC;oCAEzB,IAAI,oBAAoB;wCACtB,QAAQ,IAAI,CAAC;wCACb,wBAAwB;wCACxB;oCACF;oCAEA,eAAe;oCACf,qBAAW,CAAC,WAAW;oCACvB,aAAO,CAAC,KAAK,CAAC;oCACd,YAAO,CAAC,IAAI,CAAC;oCACb;gCACF;gCAEA,SAAS;gCACT,IAAI,cAAc,KAAK;oCACrB,gBAAgB;oCAChB,aAAO,CAAC,KAAK,CAAC,gBAAgB;oCAC9B;gCACF;gCAEA,WAAW;gCACX,OAAQ,UAAU,QAAQ;oCACxB;wCAEE;oCACF;wCACE,aAAO,CAAC,OAAO,CAAC;wCAChB;oCACF;wCACE,aAAO,CAAC,KAAK,CAAC;wCACd;oCACF;wCACE,kBAAY,CAAC,IAAI,CAAC;4CAChB,aAAa;4CACb,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC;wCAC/B;wCACA;oCACF;wCACE,YAAO,CAAC,IAAI,CAAC;wCACb;oCACF;wCACE,aAAO,CAAC,KAAK,CAAC;gCAClB;4BACF;wBACF,OAEK,IAAI,MAAM,IAAI,KAAK,YAAY;4BAClC,MAAM,YAA2C,MAAM,IAAI;4BAC3D,IAAI,WAAW;gCACb,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG;gCACpC,OAAQ,UAAU,QAAQ;oCACxB;wCACE;oCACF;wCACE,aAAO,CAAC,OAAO,CAAC;wCAChB;oCACF;wCACE,aAAO,CAAC,KAAK,CAAC;wCACd;oCACF;wCACE,kBAAY,CAAC,IAAI,CAAC;4CAChB,aAAa;4CACb,SAAS;wCACX;wCACA;oCACF;wCACE,YAAO,CAAC,IAAI,CAAC;wCACb;oCACF;wCACE,aAAO,CAAC,KAAK,CAAC;gCAClB;4BACF;wBACF,OAEK,IAAI,MAAM,QAAQ,EAAE;4BACvB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;4BACjC,IAAI,WAAW,KAAK;gCAClB,qBAAW,CAAC,WAAW;gCACvB,aAAO,CAAC,KAAK,CAAC;gCACd,YAAO,CAAC,IAAI,CAAC;4BACf,OAAO,IAAI,WAAW,KACpB,aAAO,CAAC,KAAK,CAAC;iCACT,IAAI,WAAW,KACpB,aAAO,CAAC,KAAK,CAAC;iCACT,IAAI,UAAU,KACnB,aAAO,CAAC,KAAK,CAAC;iCAEd,aAAO,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC;wBAEnC,OAEK,IAAI,MAAM,OAAO,EACpB,aAAO,CAAC,KAAK,CAAC;6BAId,aAAO,CAAC,KAAK,CAAC;oBAElB;gBACF;gBAEA,QAAQ;gBACR,qBAAqB;oBACnB,CAAC;4BAWM;wBAVL,QAAQ;wBACR,MAAM,QAAQ,qBAAW,CAAC,QAAQ;wBAClC,IAAI,OACF,OAAO,OAAO,GAAG;4BACf,GAAG,OAAO,OAAO;4BACjB,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC;wBAClC;wBAGF,kBAAkB;wBAClB,IAAI,GAAC,kBAAA,OAAO,OAAO,cAAd,sCAAA,eAAgB,CAAC,eAAe,GACnC,OAAO,OAAO,GAAG;4BACf,GAAG,OAAO,OAAO;4BACjB,gBAAgB;wBAClB;wBAGF,OAAO;oBACT;iBACD;gBAED,QAAQ;gBACR,sBAAsB;oBACpB,CAAC;wBACC,iBAAiB;wBACjB,MAAM,EAAE,IAAI,EAAE,GAAG;wBAEjB,kBAAkB;wBAClB,IAAI,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,WAAW;4BAC5B,eAAe;4BACf,IAAI,KAAK,IAAI,KAAK,KAChB,OAAO;wBAET,0BAA0B;wBAC5B;wBAEA,OAAO;oBACT;iBACD;YACH;;;;;;;;;;;;;;;;;;;;;;;IFtOc;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}