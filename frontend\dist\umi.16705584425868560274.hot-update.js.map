{"version": 3, "sources": ["umi.16705584425868560274.hot-update.js", "src/app.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='5264699545402795781';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';\nimport { history } from '@umijs/max';\nimport {\n  ErrorBoundary,\n  Footer,\n} from '@/components';\nimport UserFloatButton from '@/components/FloatButton';\nimport { AuthService, UserService, TeamService } from '@/services';\nimport type { UserProfileResponse, TeamDetailResponse } from '@/types/api';\nimport { hasTeamInCurrentToken } from '@/utils/tokenUtils';\nimport { setupDevVerificationCodeListener, showDevDebugPanel } from '@/utils/devHelper';\nimport { errorConfig } from '@/requestErrorConfig';\n\n// 全局初始化数据配置，用于 Layout 用户信息和权限初始化\n// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate\nexport async function getInitialState(): Promise<{\n  currentUser?: UserProfileResponse;\n  currentTeam?: TeamDetailResponse;\n  loading?: boolean;\n  fetchUserInfo?: () => Promise<UserProfileResponse | undefined>;\n  fetchTeamInfo?: () => Promise<TeamDetailResponse | undefined>;\n}> {\n  const fetchUserInfo = async (): Promise<UserProfileResponse | undefined> => {\n    try {\n      return await UserService.getUserProfile();\n    } catch (error) {\n      console.error('获取用户信息失败:', error);\n      // 如果获取用户信息失败，可能是token过期，清除登录状态\n      AuthService.clearToken();\n      return undefined;\n    }\n  };\n\n  const fetchTeamInfo = async (): Promise<TeamDetailResponse | undefined> => {\n    try {\n      // 只有当Token中包含团队信息时才尝试获取团队详情\n      if (!hasTeamInCurrentToken()) {\n        return undefined;\n      }\n      return await TeamService.getCurrentTeamDetail();\n    } catch (error) {\n      console.error('获取团队信息失败:', error);\n      return undefined;\n    }\n  };\n\n\n\n  // 如果不是登录页面，执行\n  const { location } = history;\n  if (!['/user/login', '/user/register'].includes(location.pathname)) {\n    try {\n      // 检查登录状态\n      if (!AuthService.isLoggedIn()) {\n        return {\n          fetchUserInfo,\n          fetchTeamInfo,\n        };\n      }\n\n      // 获取用户信息\n      const currentUser = await fetchUserInfo();\n      if (!currentUser) {\n        return {\n          fetchUserInfo,\n          fetchTeamInfo,\n        };\n      }\n\n      // 尝试获取团队信息（如果Token中包含团队信息）\n      const currentTeam = await fetchTeamInfo();\n\n      return {\n        fetchUserInfo,\n        fetchTeamInfo,\n        currentUser,\n        currentTeam,\n      };\n    } catch (error) {\n      console.error('初始化失败:', error);\n      return {\n        fetchUserInfo,\n        fetchTeamInfo,\n      };\n    }\n  }\n\n  // 初始化开发环境调试工具\n  if (process.env.NODE_ENV === 'development') {\n    setupDevVerificationCodeListener();\n    // 延迟显示调试面板，避免在初始化时显示\n    setTimeout(() => {\n      if (window.location.pathname.includes('/user/login')) {\n        showDevDebugPanel();\n      }\n    }, 1000);\n  }\n\n  return {\n    fetchUserInfo,\n    fetchTeamInfo,\n  };\n}\n\n// ProLayout 支持的api https://procomponents.ant.design/components/layout\nexport const layout: RunTimeLayoutConfig = ({ initialState }) => {\n  return {\n    // 水印\n    waterMarkProps: {\n      content: initialState?.currentUser?.name,\n    },\n    // 底部版权信息\n    footerRender: () => <Footer />,\n    // 移除右侧内容渲染\n    rightContentRender: false,\n    // 页面切换时触发\n    onPageChange: () => {\n      const { location } = history;\n      // 如果没有登录，重定向到 login\n      if (\n        !initialState?.currentUser &&\n        !['/user/login', '/user/register'].includes(location.pathname)\n      ) {\n        history.push('/user/login');\n      }\n    },\n    // 自定义布局区域的背景颜色、文字颜色等\n    bgLayoutImgList: [\n      {\n        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',\n        left: 85,\n        bottom: 100,\n        height: '303px',\n      },\n      {\n        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',\n        bottom: -68,\n        right: -45,\n        height: '303px',\n      },\n      {\n        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',\n        bottom: 0,\n        left: 0,\n        width: '331px',\n      },\n    ],\n    // 自定义链接\n    links: [],\n    // 自定义菜单头\n    menuHeaderRender: undefined,\n    // 自定义 403 页面\n    // unAccessible: <div>unAccessible</div>,\n    // 增加一个 loading 的状态\n    childrenRender: (children) => {\n      return (\n        <ErrorBoundary>\n          {children}\n          {/* 全局 FloatButton - 在用户登录后显示 */}\n          <UserFloatButton />\n        </ErrorBoundary>\n      );\n    },\n    // 移除 settings 属性，使用默认配置\n  };\n};\n\n/**\n * @name request 配置，可以配置错误处理\n * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。\n * @doc https://umijs.org/docs/max/request#配置\n */\nexport const request: RequestConfig = {\n  // 其他配置\n};\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCYS,eAAe;2BAAf;;gBA0FT,MAAM;2BAAN;;gBAmEA,OAAO;2BAAP;;;;;;;wCA3KW;+CAIjB;yFACqB;6CAC0B;+CAEhB;8CAC8B;;;;;;;;;YAK7D,eAAe;gBAOpB,MAAM,gBAAgB;oBACpB,IAAI;wBACF,OAAO,MAAM,qBAAW,CAAC,cAAc;oBACzC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,+BAA+B;wBAC/B,qBAAW,CAAC,UAAU;wBACtB,OAAO;oBACT;gBACF;gBAEA,MAAM,gBAAgB;oBACpB,IAAI;wBACF,4BAA4B;wBAC5B,IAAI,CAAC,IAAA,iCAAqB,KACxB,OAAO;wBAET,OAAO,MAAM,qBAAW,CAAC,oBAAoB;oBAC/C,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,OAAO;oBACT;gBACF;gBAIA,cAAc;gBACd,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAO;gBAC5B,IAAI,CAAC;oBAAC;oBAAe;iBAAiB,CAAC,QAAQ,CAAC,SAAS,QAAQ,GAC/D,IAAI;oBACF,SAAS;oBACT,IAAI,CAAC,qBAAW,CAAC,UAAU,IACzB,OAAO;wBACL;wBACA;oBACF;oBAGF,SAAS;oBACT,MAAM,cAAc,MAAM;oBAC1B,IAAI,CAAC,aACH,OAAO;wBACL;wBACA;oBACF;oBAGF,2BAA2B;oBAC3B,MAAM,cAAc,MAAM;oBAE1B,OAAO;wBACL;wBACA;wBACA;wBACA;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,UAAU;oBACxB,OAAO;wBACL;wBACA;oBACF;gBACF;gBAKA,IAAA,2CAAgC;gBAChC,qBAAqB;gBACrB,WAAW;oBACT,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBACpC,IAAA,4BAAiB;gBAErB,GAAG;gBAGL,OAAO;oBACL;oBACA;gBACF;YACF;YAGO,MAAM,SAA8B,CAAC,EAAE,YAAY,EAAE;oBAI7C;gBAHb,OAAO;oBACL,KAAK;oBACL,gBAAgB;wBACd,OAAO,EAAE,yBAAA,oCAAA,4BAAA,aAAc,WAAW,cAAzB,gDAAA,0BAA2B,IAAI;oBAC1C;oBACA,SAAS;oBACT,cAAc,kBAAM,2BAAC,kBAAM;;;;;oBAC3B,WAAW;oBACX,oBAAoB;oBACpB,UAAU;oBACV,cAAc;wBACZ,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAO;wBAC5B,oBAAoB;wBACpB,IACE,EAAC,yBAAA,mCAAA,aAAc,WAAW,KAC1B,CAAC;4BAAC;4BAAe;yBAAiB,CAAC,QAAQ,CAAC,SAAS,QAAQ,GAE7D,YAAO,CAAC,IAAI,CAAC;oBAEjB;oBACA,qBAAqB;oBACrB,iBAAiB;wBACf;4BACE,KAAK;4BACL,MAAM;4BACN,QAAQ;4BACR,QAAQ;wBACV;wBACA;4BACE,KAAK;4BACL,QAAQ;4BACR,OAAO;4BACP,QAAQ;wBACV;wBACA;4BACE,KAAK;4BACL,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;qBACD;oBACD,QAAQ;oBACR,OAAO,EAAE;oBACT,SAAS;oBACT,kBAAkB;oBAClB,aAAa;oBACb,yCAAyC;oBACzC,mBAAmB;oBACnB,gBAAgB,CAAC;wBACf,qBACE,2BAAC,yBAAa;;gCACX;8CAED,2BAAC,oBAAe;;;;;;;;;;;oBAGtB;gBAEF;YACF;YAOO,MAAM,UAAyB;YAEtC;;;;;;;;;;;;;;;;;;;;;;;ID3Kc;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}