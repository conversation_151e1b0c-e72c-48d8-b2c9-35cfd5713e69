globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/app.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                getInitialState: function() {
                    return getInitialState;
                },
                layout: function() {
                    return layout;
                },
                request: function() {
                    return request;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _components = __mako_require__("src/components/index.ts");
            var _FloatButton = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/FloatButton/index.tsx"));
            var _services = __mako_require__("src/services/index.ts");
            var _tokenUtils = __mako_require__("src/utils/tokenUtils.ts");
            var _devHelper = __mako_require__("src/utils/devHelper.ts");
            var _requestErrorConfig = __mako_require__("src/requestErrorConfig.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            async function getInitialState() {
                const fetchUserInfo = async ()=>{
                    try {
                        return await _services.UserService.getUserProfile();
                    } catch (error) {
                        console.error('获取用户信息失败:', error);
                        // 如果获取用户信息失败，可能是token过期，清除登录状态
                        _services.AuthService.clearToken();
                        return undefined;
                    }
                };
                const fetchTeamInfo = async ()=>{
                    try {
                        // 只有当Token中包含团队信息时才尝试获取团队详情
                        if (!(0, _tokenUtils.hasTeamInCurrentToken)()) return undefined;
                        return await _services.TeamService.getCurrentTeamDetail();
                    } catch (error) {
                        console.error('获取团队信息失败:', error);
                        return undefined;
                    }
                };
                // 如果不是登录页面，执行
                const { location } = _max.history;
                if (![
                    '/user/login',
                    '/user/register'
                ].includes(location.pathname)) try {
                    // 检查登录状态
                    if (!_services.AuthService.isLoggedIn()) return {
                        fetchUserInfo,
                        fetchTeamInfo
                    };
                    // 获取用户信息
                    const currentUser = await fetchUserInfo();
                    if (!currentUser) return {
                        fetchUserInfo,
                        fetchTeamInfo
                    };
                    // 尝试获取团队信息（如果Token中包含团队信息）
                    const currentTeam = await fetchTeamInfo();
                    return {
                        fetchUserInfo,
                        fetchTeamInfo,
                        currentUser,
                        currentTeam
                    };
                } catch (error) {
                    console.error('初始化失败:', error);
                    return {
                        fetchUserInfo,
                        fetchTeamInfo
                    };
                }
                (0, _devHelper.setupDevVerificationCodeListener)();
                // 延迟显示调试面板，避免在初始化时显示
                setTimeout(()=>{
                    if (window.location.pathname.includes('/user/login')) (0, _devHelper.showDevDebugPanel)();
                }, 1000);
                return {
                    fetchUserInfo,
                    fetchTeamInfo
                };
            }
            const layout = ({ initialState })=>{
                var _initialState_currentUser;
                return {
                    // 水印
                    waterMarkProps: {
                        content: initialState === null || initialState === void 0 ? void 0 : (_initialState_currentUser = initialState.currentUser) === null || _initialState_currentUser === void 0 ? void 0 : _initialState_currentUser.name
                    },
                    // 底部版权信息
                    footerRender: ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                            fileName: "src/app.tsx",
                            lineNumber: 113,
                            columnNumber: 25
                        }, this),
                    // 移除右侧内容渲染
                    rightContentRender: false,
                    // 页面切换时触发
                    onPageChange: ()=>{
                        const { location } = _max.history;
                        // 如果没有登录，重定向到 login
                        if (!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) && ![
                            '/user/login',
                            '/user/register'
                        ].includes(location.pathname)) _max.history.push('/user/login');
                    },
                    // 自定义布局区域的背景颜色、文字颜色等
                    bgLayoutImgList: [
                        {
                            src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
                            left: 85,
                            bottom: 100,
                            height: '303px'
                        },
                        {
                            src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
                            bottom: -68,
                            right: -45,
                            height: '303px'
                        },
                        {
                            src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
                            bottom: 0,
                            left: 0,
                            width: '331px'
                        }
                    ],
                    // 自定义链接
                    links: [],
                    // 自定义菜单头
                    menuHeaderRender: undefined,
                    // 自定义 403 页面
                    // unAccessible: <div>unAccessible</div>,
                    // 增加一个 loading 的状态
                    childrenRender: (children)=>{
                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.ErrorBoundary, {
                            children: [
                                children,
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FloatButton.default, {}, void 0, false, {
                                    fileName: "src/app.tsx",
                                    lineNumber: 160,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/app.tsx",
                            lineNumber: 157,
                            columnNumber: 9
                        }, this);
                    }
                };
            };
            const request = {
                ..._requestErrorConfig.errorConfig
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/requestErrorConfig.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "errorConfig", {
                enumerable: true,
                get: function() {
                    return errorConfig;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var ErrorShowType;
            // 错误处理方案： 错误类型
            (function(ErrorShowType) {
                ErrorShowType[ErrorShowType["SILENT"] = 0] = "SILENT";
                ErrorShowType[ErrorShowType["WARN_MESSAGE"] = 1] = "WARN_MESSAGE";
                ErrorShowType[ErrorShowType["ERROR_MESSAGE"] = 2] = "ERROR_MESSAGE";
                ErrorShowType[ErrorShowType["NOTIFICATION"] = 3] = "NOTIFICATION";
                ErrorShowType[ErrorShowType["REDIRECT"] = 9] = "REDIRECT";
            })(ErrorShowType || (ErrorShowType = {}));
            const errorConfig = {
                // 错误处理： umi@3 的错误处理方案。
                errorConfig: {
                    // 错误抛出
                    errorThrower: (res)=>{
                        // 检查是否是我们的API响应格式
                        const apiResponse = res;
                        if (apiResponse.code !== undefined) // 使用我们的API响应格式
                        {
                            if (apiResponse.code !== 200) {
                                const error = new Error(apiResponse.message);
                                error.name = 'ApiError';
                                error.info = {
                                    errorCode: apiResponse.code,
                                    errorMessage: apiResponse.message,
                                    data: apiResponse.data,
                                    showType: apiResponse.code === 401 ? 9 : 2
                                };
                                throw error;
                            }
                        } else {
                            // 兼容原有格式
                            const { success, data, errorCode, errorMessage, showType } = res;
                            if (!success) {
                                const error = new Error(errorMessage);
                                error.name = 'BizError';
                                error.info = {
                                    errorCode,
                                    errorMessage,
                                    showType,
                                    data
                                };
                                throw error;
                            }
                        }
                    },
                    // 错误接收及处理
                    errorHandler: (error, opts)=>{
                        if (opts === null || opts === void 0 ? void 0 : opts.skipErrorHandler) throw error;
                        // 我们的 API 错误
                        if (error.name === 'ApiError') {
                            const errorInfo = error.info;
                            if (errorInfo) {
                                const { errorMessage, errorCode } = errorInfo;
                                // 处理认证错误
                                if (errorCode === 401) {
                                    // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
                                    const currentPath = window.location.pathname;
                                    const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');
                                    if (isDashboardRelated) {
                                        console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
                                        // 不立即清除Token和跳转，让页面自己处理
                                        return;
                                    }
                                    // 其他页面立即处理认证错误
                                    _services.AuthService.clearTokens();
                                    _antd.message.error('登录已过期，请重新登录');
                                    _max.history.push('/user/login');
                                    return;
                                }
                                // 处理权限错误
                                if (errorCode === 403) {
                                    // 显示后端返回的具体错误消息
                                    _antd.message.error(errorMessage || '没有权限访问该资源');
                                    return;
                                }
                                // 处理其他业务错误
                                switch(errorInfo.showType){
                                    case 0:
                                        break;
                                    case 1:
                                        _antd.message.warning(errorMessage);
                                        break;
                                    case 2:
                                        _antd.message.error(errorMessage);
                                        break;
                                    case 3:
                                        _antd.notification.open({
                                            description: errorMessage,
                                            message: `错误代码: ${errorCode}`
                                        });
                                        break;
                                    case 9:
                                        _max.history.push('/user/login');
                                        break;
                                    default:
                                        _antd.message.error(errorMessage);
                                }
                            }
                        } else if (error.name === 'BizError') {
                            const errorInfo = error.info;
                            if (errorInfo) {
                                const { errorMessage, errorCode } = errorInfo;
                                switch(errorInfo.showType){
                                    case 0:
                                        break;
                                    case 1:
                                        _antd.message.warning(errorMessage);
                                        break;
                                    case 2:
                                        _antd.message.error(errorMessage);
                                        break;
                                    case 3:
                                        _antd.notification.open({
                                            description: errorMessage,
                                            message: errorCode
                                        });
                                        break;
                                    case 9:
                                        _max.history.push('/user/login');
                                        break;
                                    default:
                                        _antd.message.error(errorMessage);
                                }
                            }
                        } else if (error.response) {
                            const { status } = error.response;
                            if (status === 401) {
                                _services.AuthService.clearTokens();
                                _antd.message.error('登录已过期，请重新登录');
                                _max.history.push('/user/login');
                            } else if (status === 403) _antd.message.error('没有权限访问该资源');
                            else if (status === 404) _antd.message.error('请求的资源不存在');
                            else if (status >= 500) _antd.message.error('服务器错误，请稍后重试');
                            else _antd.message.error(`请求失败: ${status}`);
                        } else if (error.request) _antd.message.error('网络错误，请检查网络连接');
                        else _antd.message.error('请求失败，请重试');
                    }
                },
                // 请求拦截器
                requestInterceptors: [
                    (config)=>{
                        var _config_headers;
                        // 添加认证头
                        const token = _services.AuthService.getToken();
                        if (token) config.headers = {
                            ...config.headers,
                            Authorization: `Bearer ${token}`
                        };
                        // 确保 Content-Type
                        if (!((_config_headers = config.headers) === null || _config_headers === void 0 ? void 0 : _config_headers['Content-Type'])) config.headers = {
                            ...config.headers,
                            'Content-Type': 'application/json'
                        };
                        return config;
                    }
                ],
                // 响应拦截器
                responseInterceptors: [
                    (response)=>{
                        // 拦截响应数据，进行个性化处理
                        const { data } = response;
                        // 检查是否是我们的API响应格式
                        if ((data === null || data === void 0 ? void 0 : data.code) !== undefined) {
                            // 如果是成功响应，直接返回
                            if (data.code === 200) return response;
                        // 错误响应会在 errorThrower 中处理
                        }
                        return response;
                    }
                ]
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '7315735437539561497';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.5264699545402795781.hot-update.js.map