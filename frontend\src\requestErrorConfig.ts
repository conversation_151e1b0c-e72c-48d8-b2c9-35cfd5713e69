﻿import type { RequestOptions } from '@@/plugin-request/request';
import type { RequestConfig } from '@umijs/max';
import { history } from '@umijs/max';
import { message, notification } from 'antd';
import { AuthService } from '@/services';

// 错误处理方案： 错误类型
enum ErrorShowType {
  SILENT = 0,
  WARN_MESSAGE = 1,
  ERROR_MESSAGE = 2,
  NOTIFICATION = 3,
  REDIRECT = 9,
}

// 与后端约定的响应数据格式
interface ApiResponseStructure {
  code: number;
  message: string;
  data: any;
  timestamp: string;
}

// 兼容原有格式
interface ResponseStructure {
  success: boolean;
  data: any;
  errorCode?: number;
  errorMessage?: string;
  showType?: ErrorShowType;
}

/**
 * @name 错误处理
 * 适配团队管理系统的错误处理机制
 * @doc https://umijs.org/docs/max/request#配置
 */
export const errorConfig: RequestConfig = {
  // 错误处理： umi@3 的错误处理方案。
  errorConfig: {
    // 错误抛出
    errorThrower: (res) => {
      // 检查是否是我们的API响应格式
      const apiResponse = res as unknown as ApiResponseStructure;
      if (apiResponse.code !== undefined) {
        // 使用我们的API响应格式
        if (apiResponse.code !== 200) {
          const error: any = new Error(apiResponse.message);
          error.name = 'ApiError';
          error.info = {
            errorCode: apiResponse.code,
            errorMessage: apiResponse.message,
            data: apiResponse.data,
            showType:
              apiResponse.code === 401
                ? ErrorShowType.REDIRECT
                : ErrorShowType.ERROR_MESSAGE,
          };
          throw error;
        }
      } else {
        // 兼容原有格式
        const { success, data, errorCode, errorMessage, showType } =
          res as unknown as ResponseStructure;
        if (!success) {
          const error: any = new Error(errorMessage);
          error.name = 'BizError';
          error.info = { errorCode, errorMessage, showType, data };
          throw error;
        }
      }
    },
    // 错误接收及处理
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;

      // 我们的 API 错误
      if (error.name === 'ApiError') {
        const errorInfo = error.info;
        if (errorInfo) {
          const { errorMessage, errorCode } = errorInfo;

          // 处理认证错误
          if (errorCode === 401) {
            // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
            const currentPath = window.location.pathname;
            const isDashboardRelated =
              currentPath.startsWith('/dashboard') ||
              currentPath.startsWith('/team');

            if (isDashboardRelated) {
              console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
              // 不立即清除Token和跳转，让页面自己处理
              return;
            }

            // 其他页面立即处理认证错误
            AuthService.clearTokens();
            message.error('登录已过期，请重新登录');
            history.push('/user/login');
            return;
          }

          // 处理权限错误
          if (errorCode === 403) {
            // 显示后端返回的具体错误消息
            message.error(errorMessage || '没有权限访问该资源');
            return;
          }

          // 处理其他业务错误
          switch (errorInfo.showType) {
            case ErrorShowType.SILENT:
              // do nothing
              break;
            case ErrorShowType.WARN_MESSAGE:
              message.warning(errorMessage);
              break;
            case ErrorShowType.ERROR_MESSAGE:
              message.error(errorMessage);
              break;
            case ErrorShowType.NOTIFICATION:
              notification.open({
                description: errorMessage,
                message: `错误代码: ${errorCode}`,
              });
              break;
            case ErrorShowType.REDIRECT:
              history.push('/user/login');
              break;
            default:
              message.error(errorMessage);
          }
        }
      }
      // 兼容原有的 BizError
      else if (error.name === 'BizError') {
        const errorInfo: ResponseStructure | undefined = error.info;
        if (errorInfo) {
          const { errorMessage, errorCode } = errorInfo;
          switch (errorInfo.showType) {
            case ErrorShowType.SILENT:
              break;
            case ErrorShowType.WARN_MESSAGE:
              message.warning(errorMessage);
              break;
            case ErrorShowType.ERROR_MESSAGE:
              message.error(errorMessage);
              break;
            case ErrorShowType.NOTIFICATION:
              notification.open({
                description: errorMessage,
                message: errorCode,
              });
              break;
            case ErrorShowType.REDIRECT:
              history.push('/user/login');
              break;
            default:
              message.error(errorMessage);
          }
        }
      }
      // HTTP 错误
      else if (error.response) {
        const { status } = error.response;
        if (status === 401) {
          AuthService.clearTokens();
          message.error('登录已过期，请重新登录');
          history.push('/user/login');
        } else if (status === 403) {
          message.error('没有权限访问该资源');
        } else if (status === 404) {
          message.error('请求的资源不存在');
        } else if (status >= 500) {
          message.error('服务器错误，请稍后重试');
        } else {
          message.error(`请求失败: ${status}`);
        }
      }
      // 网络错误
      else if (error.request) {
        message.error('网络错误，请检查网络连接');
      }
      // 其他错误
      else {
        message.error('请求失败，请重试');
      }
    },
  },

  // 请求拦截器
  requestInterceptors: [
    (config: RequestOptions) => {
      // 添加认证头
      const token = AuthService.getToken();
      if (token) {
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${token}`,
        };
      }

      // 确保 Content-Type
      if (!config.headers?.['Content-Type']) {
        config.headers = {
          ...config.headers,
          'Content-Type': 'application/json',
        };
      }

      return config;
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    (response) => {
      // 拦截响应数据，进行个性化处理
      const { data } = response;

      // 检查是否是我们的API响应格式
      if (data?.code !== undefined) {
        // 如果是成功响应，直接返回
        if (data.code === 200) {
          return response;
        }
        // 错误响应会在 errorThrower 中处理
      }

      return response;
    },
  ],
};
