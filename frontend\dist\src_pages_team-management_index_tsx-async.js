((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['src/pages/team-management/index.tsx'],
{ "src/pages/team-management/components/TeamInvitationList.tsx": function (module, exports, __mako_require__){
/**
 * 团队邀请列表组件
 * 
 * 功能特性：
 * - 显示团队发出的所有邀请记录
 * - 支持取消待处理的邀请
 * - 显示邀请状态和过期时间
 * - 支持搜索和筛选
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _dayjs = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/dayjs/dayjs.min.js"));
var _services = __mako_require__("src/services/index.ts");
var _api = __mako_require__("src/types/api.ts");
var _InvitationStatus = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/InvitationStatus.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const { Option } = _antd.Select;
const TeamInvitationList = ({ onRefresh })=>{
    _s();
    const [invitations, setInvitations] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(false);
    const [searchText, setSearchText] = (0, _react.useState)('');
    const [statusFilter, setStatusFilter] = (0, _react.useState)('');
    // 获取邀请列表
    const fetchInvitations = async ()=>{
        try {
            setLoading(true);
            const invitationList = await _services.InvitationService.getCurrentTeamInvitations();
            setInvitations(invitationList || []);
        } catch (error) {
            console.error('获取邀请列表失败:', error);
            _antd.message.error('获取邀请列表失败');
            setInvitations([]);
        } finally{
            setLoading(false);
        }
    };
    (0, _react.useEffect)(()=>{
        fetchInvitations();
    }, []);
    // 取消邀请
    const handleCancelInvitation = async (invitationId)=>{
        try {
            await _services.InvitationService.cancelInvitation(invitationId);
            _antd.message.success('邀请取消成功');
            fetchInvitations();
            onRefresh === null || onRefresh === void 0 || onRefresh();
        } catch (error) {
            console.error('取消邀请失败:', error);
            _antd.message.error('取消邀请失败');
        }
    };
    // 过滤邀请列表
    const filteredInvitations = invitations.filter((invitation)=>{
        const matchesSearch = !searchText || invitation.inviteeEmail.toLowerCase().includes(searchText.toLowerCase()) || invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(searchText.toLowerCase());
        const matchesStatus = !statusFilter || invitation.status === statusFilter;
        return matchesSearch && matchesStatus;
    });
    // 表格列定义
    const columns = [
        {
            title: '被邀请人',
            key: 'invitee',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    direction: "vertical",
                    size: 0,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            strong: true,
                            children: record.inviteeName || '未注册用户'
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                            lineNumber: 105,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            style: {
                                fontSize: '12px'
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                    lineNumber: 107,
                                    columnNumber: 13
                                }, this),
                                " ",
                                record.inviteeEmail
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                            lineNumber: 106,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                    lineNumber: 104,
                    columnNumber: 9
                }, this)
        },
        {
            title: '邀请状态',
            dataIndex: 'status',
            key: 'status',
            render: (status, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_InvitationStatus.default, {
                    status: status,
                    isExpired: record.isExpired
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                    lineNumber: 117,
                    columnNumber: 9
                }, this),
            filters: [
                {
                    text: '待确认',
                    value: _api.InvitationStatus.PENDING
                },
                {
                    text: '已接受',
                    value: _api.InvitationStatus.ACCEPTED
                },
                {
                    text: '已拒绝',
                    value: _api.InvitationStatus.REJECTED
                },
                {
                    text: '已过期',
                    value: _api.InvitationStatus.EXPIRED
                },
                {
                    text: '已取消',
                    value: _api.InvitationStatus.CANCELLED
                }
            ],
            onFilter: (value, record)=>record.status === value
        },
        {
            title: '邀请时间',
            dataIndex: 'invitedAt',
            key: 'invitedAt',
            render: (time)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                    children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                    lineNumber: 136,
                    columnNumber: 9
                }, this),
            sorter: (a, b)=>(0, _dayjs.default)(a.invitedAt).unix() - (0, _dayjs.default)(b.invitedAt).unix()
        },
        {
            title: '过期时间',
            dataIndex: 'expiresAt',
            key: 'expiresAt',
            render: (time, record)=>{
                const isExpired = record.isExpired;
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                        type: isExpired ? 'danger' : 'secondary',
                        children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                        lineNumber: 150,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                    lineNumber: 149,
                    columnNumber: 11
                }, this);
            }
        },
        {
            title: '响应时间',
            dataIndex: 'respondedAt',
            key: 'respondedAt',
            render: (time)=>time ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                    children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                    lineNumber: 162,
                    columnNumber: 9
                }, this) : '-'
        },
        {
            title: '邀请链接',
            key: 'invitationLink',
            render: (_, record)=>{
                if (!record.invitationLink) return '-';
                const copyLink = ()=>{
                    navigator.clipboard.writeText(record.invitationLink).then(()=>{
                        _antd.message.success('邀请链接已复制到剪贴板');
                    }).catch(()=>{
                        _antd.message.error('复制失败，请手动复制');
                    });
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                            title: "复制邀请链接",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CopyOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                    lineNumber: 186,
                                    columnNumber: 23
                                }, void 0),
                                onClick: copyLink,
                                children: "复制链接"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 184,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                            lineNumber: 183,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                            title: record.invitationLink,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LinkOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                    lineNumber: 195,
                                    columnNumber: 23
                                }, void 0),
                                onClick: ()=>window.open(record.invitationLink, '_blank'),
                                children: "预览"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 193,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                            lineNumber: 192,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                    lineNumber: 182,
                    columnNumber: 11
                }, this);
            }
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: record.canBeCancelled && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                        title: "确定要取消这个邀请吗？",
                        description: "取消后被邀请人将无法通过此邀请加入团队",
                        onConfirm: ()=>handleCancelInvitation(record.id),
                        okText: "确定",
                        cancelText: "取消",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            size: "small",
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 221,
                                columnNumber: 23
                            }, void 0),
                            children: "取消邀请"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                            lineNumber: 218,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                        lineNumber: 211,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                    lineNumber: 209,
                    columnNumber: 9
                }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        title: "邀请记录",
        extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                    lineNumber: 238,
                    columnNumber: 19
                }, void 0),
                onClick: fetchInvitations,
                loading: loading,
                children: "刷新"
            }, void 0, false, {
                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                lineNumber: 237,
                columnNumber: 11
            }, void 0)
        }, void 0, false, {
            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
            lineNumber: 236,
            columnNumber: 9
        }, void 0),
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                style: {
                    marginBottom: 16
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        placeholder: "搜索邮箱或姓名",
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                            lineNumber: 251,
                            columnNumber: 19
                        }, void 0),
                        value: searchText,
                        onChange: (e)=>setSearchText(e.target.value),
                        style: {
                            width: 200
                        }
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                        lineNumber: 249,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                        placeholder: "筛选状态",
                        value: statusFilter,
                        onChange: setStatusFilter,
                        style: {
                            width: 120
                        },
                        allowClear: true,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                value: _api.InvitationStatus.PENDING,
                                children: "待确认"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 263,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                value: _api.InvitationStatus.ACCEPTED,
                                children: "已接受"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 264,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                value: _api.InvitationStatus.REJECTED,
                                children: "已拒绝"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 265,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                value: _api.InvitationStatus.EXPIRED,
                                children: "已过期"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 266,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                value: _api.InvitationStatus.CANCELLED,
                                children: "已取消"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 267,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                        lineNumber: 256,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                lineNumber: 248,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                columns: columns,
                dataSource: filteredInvitations,
                rowKey: "id",
                loading: loading,
                pagination: {
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total)=>`共 ${total} 条邀请记录`,
                    pageSize: 10
                }
            }, void 0, false, {
                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                lineNumber: 272,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
        lineNumber: 233,
        columnNumber: 5
    }, this);
};
_s(TeamInvitationList, "Sx7tbJXUjergP+RFG/GYyQ2fSfU=");
_c = TeamInvitationList;
var _default = TeamInvitationList;
var _c;
$RefreshReg$(_c, "TeamInvitationList");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team-management/components/TeamMemberManagement.tsx": function (module, exports, __mako_require__){
/**
 * 团队成员管理组件
 * 
 * 功能特性：
 * - 查看团队成员列表及详细信息
 * - 添加新成员（通过邮箱邀请）
 * - 移除团队现有成员
 * - 批量操作支持
 * - 成员搜索和筛选
 * 
 * 权限控制：
 * - 只有团队创建者可以进行成员管理操作
 * - 创建者不能移除自己
 * - 提供详细的操作确认
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _team = __mako_require__("src/services/team.ts");
var _invitation = __mako_require__("src/services/invitation.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text, Title } = _antd.Typography;
const { TextArea } = _antd.Input;
const TeamMemberManagement = ({ teamDetail, onRefresh })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [members, setMembers] = (0, _react.useState)([]);
    const [searchText, setSearchText] = (0, _react.useState)('');
    const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
    const [inviteModalVisible, setInviteModalVisible] = (0, _react.useState)(false);
    const [inviteForm] = _antd.Form.useForm();
    (0, _react.useEffect)(()=>{
        fetchMembers();
    }, []);
    const fetchMembers = async ()=>{
        try {
            setLoading(true);
            const memberList = await _team.TeamService.getCurrentTeamMembers();
            setMembers(memberList || []);
        } catch (error) {
            console.error('获取团队成员失败:', error);
            _antd.message.error('获取团队成员失败');
            setMembers([]); // 确保在错误时设置为空数组
        } finally{
            setLoading(false);
        }
    };
    // 邀请新成员
    const handleInviteMembers = async (values)=>{
        try {
            const emailList = values.emails.split('\n').map((email)=>email.trim()).filter((email)=>email);
            // 使用新的邀请链接功能
            const response = await _invitation.InvitationService.sendInvitations({
                emails: emailList,
                message: values.message
            });
            // 显示详细的发送结果
            if (response.successCount > 0) {
                _antd.message.success(`成功发送 ${response.successCount} 个邀请`);
                console.log('邀请链接:', response.invitations.map((inv)=>({
                        email: inv.email,
                        link: inv.invitationLink
                    })));
            }
            if (response.failureCount > 0) _antd.message.warning(`${response.failureCount} 个邀请发送失败`);
            setInviteModalVisible(false);
            inviteForm.resetFields();
            fetchMembers();
        } catch (error) {
            console.error('邀请成员失败:', error);
            _antd.message.error('邀请成员失败');
        }
    };
    // 移除单个成员
    const handleRemoveMember = async (member)=>{
        try {
            await _team.TeamService.removeMember(member.id);
            _antd.message.success(`已移除成员：${member.name}`);
            fetchMembers();
            onRefresh();
        } catch (error) {
            console.error('移除成员失败:', error);
            _antd.message.error('移除成员失败');
        }
    };
    // 批量移除成员
    const handleBatchRemove = async ()=>{
        try {
            const memberIds = selectedRowKeys;
            for (const memberId of memberIds)await _team.TeamService.removeMember(memberId);
            _antd.message.success(`已移除 ${memberIds.length} 名成员`);
            setSelectedRowKeys([]);
            fetchMembers();
            onRefresh();
        } catch (error) {
            console.error('批量移除成员失败:', error);
            _antd.message.error('批量移除成员失败');
        }
    };
    // 筛选成员
    const filteredMembers = (members || []).filter((member)=>member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase()));
    // 停用/启用成员
    const handleToggleMemberStatus = async (member, isActive)=>{
        try {
            await _team.TeamService.updateMemberStatus(member.id, isActive);
            _antd.message.success(`已${isActive ? '启用' : '停用'}成员：${member.name}`);
            fetchMembers();
            onRefresh();
        } catch (error) {
            console.error('更新成员状态失败:', error);
            _antd.message.error('更新成员状态失败');
        }
    };
    // 表格列配置
    const columns = [
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            render: (name, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            strong: true,
                            children: name
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 187,
                            columnNumber: 11
                        }, this),
                        record.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 189,
                                columnNumber: 24
                            }, void 0),
                            color: "gold",
                            children: "创建者"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 189,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 186,
                    columnNumber: 9
                }, this)
        },
        {
            title: '邮箱',
            dataIndex: 'email',
            key: 'email',
            render: (email)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: email
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 199,
                    columnNumber: 9
                }, this)
        },
        {
            title: '状态',
            dataIndex: 'isActive',
            key: 'status',
            width: 100,
            render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isActive ? 'green' : 'red',
                    children: isActive ? '启用' : '停用'
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 208,
                    columnNumber: 9
                }, this)
        },
        {
            title: '加入时间',
            dataIndex: 'assignedAt',
            key: 'assignedAt',
            width: 150,
            render: (date)=>new Date(date).toLocaleDateString()
        },
        {
            title: '最后访问',
            dataIndex: 'lastAccessTime',
            key: 'lastAccessTime',
            width: 150,
            render: (date)=>new Date(date).toLocaleDateString()
        },
        {
            title: '操作',
            key: 'action',
            width: 200,
            render: (_, record)=>{
                if (record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "-"
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 233,
                    columnNumber: 18
                }, this);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        record.isActive ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "text",
                            size: "small",
                            onClick: ()=>handleToggleMemberStatus(record, false),
                            children: "停用"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 239,
                            columnNumber: 15
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "text",
                            size: "small",
                            onClick: ()=>handleToggleMemberStatus(record, true),
                            children: "启用"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 247,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                            title: "确认移除成员",
                            description: `确定要移除成员 ${record.name} 吗？此操作不可恢复。`,
                            onConfirm: ()=>handleRemoveMember(record),
                            okText: "确认",
                            cancelText: "取消",
                            okType: "danger",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                danger: true,
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 267,
                                    columnNumber: 23
                                }, void 0),
                                children: "移除"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 263,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 255,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 237,
                    columnNumber: 11
                }, this);
            }
        }
    ];
    // 行选择配置
    const rowSelection = {
        selectedRowKeys,
        onChange: setSelectedRowKeys,
        getCheckboxProps: (record)=>({
                disabled: record.isCreator
            })
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                gutter: 16,
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        span: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "团队成员总数",
                                value: (members || []).length,
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 296,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 293,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 292,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                        lineNumber: 291,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        span: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "活跃成员",
                                value: (members || []).filter((m)=>m.isActive).length,
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 305,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 302,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 301,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                        lineNumber: 300,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        span: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "待激活成员",
                                value: (members || []).filter((m)=>!m.isActive).length,
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 314,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 311,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 310,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                        lineNumber: 309,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        span: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "管理员",
                                value: (members || []).filter((m)=>m.isCreator).length,
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 323,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 320,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 319,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                        lineNumber: 318,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                lineNumber: 290,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                style: {
                    marginBottom: 16
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    justify: "space-between",
                    align: "middle",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "搜索成员姓名或邮箱",
                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 336,
                                            columnNumber: 25
                                        }, void 0),
                                        value: searchText,
                                        onChange: (e)=>setSearchText(e.target.value),
                                        style: {
                                            width: 300
                                        },
                                        allowClear: true
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 334,
                                        columnNumber: 15
                                    }, this),
                                    selectedRowKeys.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                        title: "批量移除成员",
                                        description: `确定要移除选中的 ${selectedRowKeys.length} 名成员吗？此操作不可恢复。`,
                                        onConfirm: handleBatchRemove,
                                        okText: "确认",
                                        cancelText: "取消",
                                        okType: "danger",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            danger: true,
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 353,
                                                columnNumber: 27
                                            }, void 0),
                                            children: [
                                                "批量移除 (",
                                                selectedRowKeys.length,
                                                ")"
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 351,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 343,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 333,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 332,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 364,
                                    columnNumber: 21
                                }, void 0),
                                onClick: ()=>setInviteModalVisible(true),
                                children: "邀请成员"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 362,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 361,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 331,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                lineNumber: 330,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                    columns: columns,
                    dataSource: filteredMembers,
                    rowKey: "id",
                    loading: loading,
                    rowSelection: rowSelection,
                    pagination: {
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total)=>`共 ${total} 名成员`,
                        pageSize: 10
                    }
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 375,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                lineNumber: 374,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "邀请新成员",
                open: inviteModalVisible,
                onCancel: ()=>{
                    setInviteModalVisible(false);
                    inviteForm.resetFields();
                },
                footer: null,
                width: 600,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: inviteForm,
                    layout: "vertical",
                    onFinish: handleInviteMembers,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "emails",
                            label: "邮箱地址",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入邮箱地址'
                                }
                            ],
                            extra: "每行一个邮箱地址，支持批量邀请",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 6,
                                placeholder: "请输入邮箱地址，每行一个 例如： <EMAIL> <EMAIL>"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 414,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 406,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "message",
                            label: "邀请消息（可选）",
                            extra: "您可以添加一些邀请消息，让被邀请人更好地了解邀请意图",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 3,
                                placeholder: "欢迎加入我们的团队！我们期待与您一起工作...",
                                maxLength: 500,
                                showCount: true
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 424,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 419,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 433,
                                            columnNumber: 62
                                        }, void 0),
                                        children: "发送邀请"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 433,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>setInviteModalVisible(false),
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 436,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 432,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 431,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 401,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                lineNumber: 391,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
        lineNumber: 288,
        columnNumber: 5
    }, this);
};
_s(TeamMemberManagement, "lwyw8TlHxmNVMk/bokFh1nGrhdE=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = TeamMemberManagement;
var _default = TeamMemberManagement;
var _c;
$RefreshReg$(_c, "TeamMemberManagement");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team-management/components/TeamSettings.tsx": function (module, exports, __mako_require__){
/**
 * 团队设置组件
 * 
 * 功能特性：
 * - 编辑/修改团队名称和描述
 * - 删除整个团队
 * - 团队基本信息管理
 * - 危险操作确认
 * 
 * 权限控制：
 * - 只有团队创建者可以进行设置操作
 * - 删除团队需要二次确认
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _team = __mako_require__("src/services/team.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const { TextArea } = _antd.Input;
const TeamSettings = ({ teamDetail, onRefresh })=>{
    _s();
    const [editMode, setEditMode] = (0, _react.useState)(false);
    const [updating, setUpdating] = (0, _react.useState)(false);
    const [deleteModalVisible, setDeleteModalVisible] = (0, _react.useState)(false);
    const [deleteConfirmText, setDeleteConfirmText] = (0, _react.useState)('');
    const [deleting, setDeleting] = (0, _react.useState)(false);
    const [form] = _antd.Form.useForm();
    // 进入编辑模式
    const handleEdit = ()=>{
        form.setFieldsValue({
            name: teamDetail.name,
            description: teamDetail.description || ''
        });
        setEditMode(true);
    };
    // 取消编辑
    const handleCancelEdit = ()=>{
        setEditMode(false);
        form.resetFields();
    };
    // 保存团队信息
    const handleSaveTeam = async (values)=>{
        try {
            setUpdating(true);
            await _team.TeamService.updateCurrentTeam(values);
            _antd.message.success('团队信息更新成功');
            setEditMode(false);
            onRefresh();
        } catch (error) {
            console.error('更新团队失败:', error);
            _antd.message.error('更新团队失败');
        } finally{
            setUpdating(false);
        }
    };
    // 删除团队
    const handleDeleteTeam = async ()=>{
        if (deleteConfirmText !== teamDetail.name) {
            _antd.message.error('请输入正确的团队名称');
            return;
        }
        try {
            setDeleting(true);
            await _team.TeamService.deleteCurrentTeam();
            _antd.message.success('团队已删除');
            setDeleteModalVisible(false);
            // 删除成功后跳转到团队选择页面
            _max.history.push('/user/team-select');
        } catch (error) {
            console.error('删除团队失败:', error);
            _antd.message.error('删除团队失败');
        } finally{
            setDeleting(false);
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                gutter: 16,
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        span: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "团队ID",
                                value: teamDetail.id,
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 129,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                lineNumber: 126,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 125,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                        lineNumber: 124,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        span: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "成员数量",
                                value: teamDetail.memberCount,
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 138,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                lineNumber: 135,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 134,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                        lineNumber: 133,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        span: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "创建时间",
                                value: new Date(teamDetail.createdAt).toLocaleDateString(),
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 147,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                lineNumber: 144,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 143,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                        lineNumber: 142,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        span: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "最后更新",
                                value: new Date(teamDetail.updatedAt).toLocaleDateString(),
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 156,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                lineNumber: 153,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 152,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                        lineNumber: 151,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                lineNumber: 123,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: "团队信息",
                extra: !editMode && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                        lineNumber: 169,
                        columnNumber: 21
                    }, void 0),
                    onClick: handleEdit,
                    children: "编辑"
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                    lineNumber: 167,
                    columnNumber: 13
                }, void 0),
                style: {
                    marginBottom: 24
                },
                children: editMode ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    layout: "vertical",
                    onFinish: handleSaveTeam,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "name",
                            label: "团队名称",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称'
                                },
                                {
                                    min: 2,
                                    max: 50,
                                    message: '团队名称长度应在2-50个字符之间'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                lineNumber: 192,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 184,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "description",
                            label: "团队描述",
                            rules: [
                                {
                                    max: 200,
                                    message: '团队描述不能超过200个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入团队描述（可选）",
                                showCount: true,
                                maxLength: 200
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                lineNumber: 201,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 194,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        loading: updating,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SaveOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                            lineNumber: 214,
                                            columnNumber: 25
                                        }, void 0),
                                        children: "保存"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                        lineNumber: 210,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: handleCancelEdit,
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                        lineNumber: 218,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                lineNumber: 209,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 208,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                    lineNumber: 179,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "团队名称："
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 227,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 4,
                                    style: {
                                        margin: '8px 0'
                                    },
                                    children: teamDetail.name
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 228,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 226,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "团队描述："
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 231,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                    style: {
                                        marginTop: 8
                                    },
                                    children: teamDetail.description || '暂无描述'
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 232,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 230,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                    lineNumber: 225,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                lineNumber: 163,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.WarningOutlined, {
                            style: {
                                color: '#ff4d4f'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 244,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "danger",
                            children: "危险操作"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 245,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                    lineNumber: 243,
                    columnNumber: 11
                }, void 0),
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "删除团队",
                        description: "删除团队将永久移除所有团队数据，包括成员关系、设置等。此操作不可恢复，请谨慎操作。",
                        type: "error",
                        showIcon: true,
                        style: {
                            marginBottom: 16
                        }
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                        lineNumber: 249,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                        title: "确认删除团队",
                        description: "您确定要删除这个团队吗？此操作不可恢复。",
                        onConfirm: ()=>setDeleteModalVisible(true),
                        okText: "确认",
                        cancelText: "取消",
                        okType: "danger",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                lineNumber: 267,
                                columnNumber: 19
                            }, void 0),
                            size: "large",
                            children: "删除团队"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 265,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                        lineNumber: 257,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                lineNumber: 241,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                            style: {
                                color: '#ff4d4f'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 279,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "danger",
                            children: "删除团队确认"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 280,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                    lineNumber: 278,
                    columnNumber: 11
                }, void 0),
                open: deleteModalVisible,
                onCancel: ()=>{
                    setDeleteModalVisible(false);
                    setDeleteConfirmText('');
                },
                footer: null,
                width: 600,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "警告：此操作不可恢复",
                        description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                                    children: "删除团队将会："
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 295,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ul", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "永久删除团队及所有相关数据"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                            lineNumber: 297,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "移除所有团队成员"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                            lineNumber: 298,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "清除团队设置和配置"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                            lineNumber: 299,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "无法恢复任何数据"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                            lineNumber: 300,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 296,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 294,
                            columnNumber: 13
                        }, void 0),
                        type: "error",
                        showIcon: true,
                        style: {
                            marginBottom: 24
                        }
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                        lineNumber: 291,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginBottom: 16
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            strong: true,
                            children: [
                                '请输入团队名称 "',
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    code: true,
                                    children: teamDetail.name
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 311,
                                    columnNumber: 22
                                }, this),
                                '" 来确认删除：'
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 310,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                        lineNumber: 309,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        placeholder: `请输入：${teamDetail.name}`,
                        value: deleteConfirmText,
                        onChange: (e)=>setDeleteConfirmText(e.target.value),
                        style: {
                            marginBottom: 24
                        }
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                        lineNumber: 315,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'right'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    onClick: ()=>{
                                        setDeleteModalVisible(false);
                                        setDeleteConfirmText('');
                                    },
                                    children: "取消"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 324,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    danger: true,
                                    loading: deleting,
                                    disabled: deleteConfirmText !== teamDetail.name,
                                    onClick: handleDeleteTeam,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                        lineNumber: 338,
                                        columnNumber: 21
                                    }, void 0),
                                    children: "确认删除团队"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamSettings.tsx",
                                    lineNumber: 332,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamSettings.tsx",
                            lineNumber: 323,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamSettings.tsx",
                        lineNumber: 322,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/components/TeamSettings.tsx",
                lineNumber: 276,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team-management/components/TeamSettings.tsx",
        lineNumber: 121,
        columnNumber: 5
    }, this);
};
_s(TeamSettings, "qBFmqaQAHbFsoHBaFFmepnnxP14=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = TeamSettings;
var _default = TeamSettings;
var _c;
$RefreshReg$(_c, "TeamSettings");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team-management/index.tsx": function (module, exports, __mako_require__){
/**
 * 集成团队管理页面
 * 
 * 功能特性：
 * - 统一的团队管理界面，包含多个功能模块
 * - 选项卡布局，便于在不同管理功能之间切换
 * - 团队成员管理、账户管理、团队设置等功能
 * - 权限控制，只有团队创建者可以访问管理功能
 * 
 * 模块组织：
 * - 团队成员管理：查看、添加、移除团队成员
 * - 成员账户管理：管理成员权限和角色
 * - 团队设置：编辑团队信息、删除团队
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _TeamMemberManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team-management/components/TeamMemberManagement.tsx"));
var _TeamSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team-management/components/TeamSettings.tsx"));
var _TeamInvitationList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team-management/components/TeamInvitationList.tsx"));
var _team = __mako_require__("src/services/team.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const TeamManagementPage = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [teamDetail, setTeamDetail] = (0, _react.useState)(null);
    const [activeTab, setActiveTab] = (0, _react.useState)('members');
    (0, _react.useEffect)(()=>{
        fetchTeamDetail();
    }, []);
    const fetchTeamDetail = async ()=>{
        try {
            setLoading(true);
            const detail = await _team.TeamService.getCurrentTeamDetail();
            setTeamDetail(detail);
        } catch (error) {
            console.error('获取团队详情失败:', error);
            // 如果获取失败，可能是没有选择团队，跳转到个人中心页面
            _max.history.push('/personal-center');
        } finally{
            setLoading(false);
        }
    };
    // 权限检查：只有团队创建者可以访问管理功能
    const hasManagePermission = (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.isCreator) || false;
    if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                textAlign: 'center',
                padding: '50px 0'
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                    size: "large"
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 78,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        marginTop: 16
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                        type: "secondary",
                        children: "正在加载团队信息..."
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 80,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 79,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/team-management/index.tsx",
            lineNumber: 77,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team-management/index.tsx",
        lineNumber: 76,
        columnNumber: 7
    }, this);
    if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    textAlign: 'center',
                    padding: '50px 0'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.InfoCircleOutlined, {
                        style: {
                            fontSize: 48,
                            color: '#faad14',
                            marginBottom: 16
                        }
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 92,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                        level: 4,
                        children: "未找到团队信息"
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 93,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                        type: "secondary",
                        children: "请先选择一个团队"
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 94,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginTop: 16
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            onClick: ()=>_max.history.push('/personal-center'),
                            children: "返回个人中心"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 96,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 95,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 91,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "src/pages/team-management/index.tsx",
            lineNumber: 90,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team-management/index.tsx",
        lineNumber: 89,
        columnNumber: 7
    }, this);
    // 权限不足提示
    if (!hasManagePermission) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                message: "权限不足",
                description: "只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。",
                type: "warning",
                showIcon: true,
                action: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    size: "small",
                    onClick: ()=>_max.history.push('/dashboard'),
                    children: "返回首页"
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 117,
                    columnNumber: 15
                }, void 0)
            }, void 0, false, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 111,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "src/pages/team-management/index.tsx",
            lineNumber: 110,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team-management/index.tsx",
        lineNumber: 109,
        columnNumber: 7
    }, this);
    // 选项卡配置
    const tabItems = [
        {
            key: 'members',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 133,
                        columnNumber: 11
                    }, this),
                    "团队成员管理"
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 132,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberManagement.default, {
                teamDetail: teamDetail,
                onRefresh: fetchTeamDetail
            }, void 0, false, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 138,
                columnNumber: 9
            }, this)
        },
        {
            key: 'invitations',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 148,
                        columnNumber: 11
                    }, this),
                    "邀请管理"
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 147,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamInvitationList.default, {
                onRefresh: fetchTeamDetail
            }, void 0, false, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 153,
                columnNumber: 9
            }, this)
        },
        {
            key: 'settings',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 162,
                        columnNumber: 11
                    }, this),
                    "团队设置"
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 161,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamSettings.default, {
                teamDetail: teamDetail,
                onRefresh: fetchTeamDetail
            }, void 0, false, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 167,
                columnNumber: 9
            }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: setActiveTab,
                items: tabItems,
                size: "large",
                tabBarStyle: {
                    marginBottom: 24
                }
            }, void 0, false, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 178,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "src/pages/team-management/index.tsx",
            lineNumber: 177,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team-management/index.tsx",
        lineNumber: 176,
        columnNumber: 5
    }, this);
};
_s(TeamManagementPage, "MbpIoVQJb3zY91BHiEpQzi7JPmo=");
_c = TeamManagementPage;
var _default = TeamManagementPage;
var _c;
$RefreshReg$(_c, "TeamManagementPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_team-management_index_tsx-async.js.map